/**
 * Stock Movement Reports Controller
 * ------------------------------
 * Handles all tenant-specific stock movement report requests.
 */

const { REPORTS } = require("@/defs/reportDefs");
const { createReportHandler } = require("@/helpers/reportHandlerHelper");
const stockMovementService = require("@/services/stockMovementReportService");

// Transfer Report
exports.getTransferListReport = createReportHandler(
  REPORTS.TRANSFER_LIST,
  stockMovementService.getTransferListReport
);

// Dispatch Transfer Report
exports.getDispatchTransferReport = createReportHandler(
  REPORTS.DISPATCH_TRANSFER,
  stockMovementService.getDispatchTransferReport
);

// Detailed Transfer Report
exports.getDetailedTransferReport = createReportHandler(
  REPORTS.DETAILED_TRANSFER,
  stockMovementService.getDetailedTransferReport
);

// Cost of Issue vs Revenue Report
exports.getCostOfIssueVsRevenueReport = createReportHandler(
  REPORTS.COST_OF_ISSUE_VS_REVENUE,
  stockMovementService.getCostOfIssueVsRevenueReport
);

// Item Wise Stock Movement Report
exports.getItemWiseStockMovementsReport = createReportHandler(
  REPORTS.ITEM_WISE_STOCK_MOVEMENTS,
  stockMovementService.getItemWiseStockMovementsReport
);

// Short Supply Report
exports.getShortSupplyReport = createReportHandler(
  REPORTS.SHORT_SUPPLY,
  stockMovementService.getShortSupplyReport
);

// Physical Closing Report
exports.getPhysicalClosingReport = createReportHandler(
  REPORTS.PHYSICAL_CLOSING,
  stockMovementService.getPhysicalClosingReport
);

// System Closing Report
exports.getSystemClosingReport = createReportHandler(
  REPORTS.SYSTEM_CLOSING,
  stockMovementService.getSystemClosingReport
);
