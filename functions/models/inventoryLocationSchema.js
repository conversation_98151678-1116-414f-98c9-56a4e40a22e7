const Joi = require("joi");

const inventoryLocationSchema = Joi.object({
  id: Joi.string().optional(),
  name: Joi.string().max(100).required(),
  locationId: Joi.string().required(),
  locationName: Joi.string().optional(),
  tagId: Joi.string().allow("").optional(),
  tenantId: Joi.string().required(),
  nameNormalized: Joi.string().max(100).optional(),
  activeStatus: Joi.boolean().default(true),
  isDefault: Joi.boolean().optional(),
  floorIds: Joi.array().items(Joi.number()).optional(),
});

module.exports = inventoryLocationSchema;
