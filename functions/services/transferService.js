// services/transferService.js
const admin = require("firebase-admin");
const db = admin.firestore();

const { LedgerTypes } = require("@/defs/ledgerDefs");
const { FirestoreDateHelper: FD } = require("@/helpers/dateHelper");
const { handleValidation } = require("@/utils/validation");

const schema = require("@/schema/transferSchema");
const { error } = require("@/schema/transferSchema");
const { getNextTransferId, getNextDispatchId } = require("./counterService");
const { creditStock, debitStock } = require("./stockService");
const { rupeeToPaise } = require("@/utils/money");

const {
  getTransfersRepo,
  createTransfer,
  getTransferById,
  updateTransfer,
  getTransfersByLocation,
  createShortageRecord,
} = require("@/repositories/transferRepo");

const { transferStatus } = require("@/defs/transferStatusDefs");

const { getTenantById } = require("@/repositories/tenantRepo");
const { applyLedgersToDailyStock } = require("./stockMovementsService");

/**
 * Create transfer
 */

const createTransferRequest = async (data, directTransfer = false) => {
  try {
    // const tenantConfiguration = directTransfer; // TODO: handle based on requirement
    const validatedData = handleValidation(data, schema);
    if (!validatedData) return; // Schema validation failed

    if (directTransfer) {
      return await createSingleTransfer(validatedData, validatedData.items);
    }

    const stockableItems = validatedData.items.filter((item) => item.stockable);
    const nonStockableItems = validatedData.items.filter(
      (item) => !item.stockable
    );

    const transferPromises = [
      stockableItems.length &&
        createSingleTransfer(
          { ...validatedData, stockableItems: true },
          stockableItems
        ),
      nonStockableItems.length &&
        createSingleTransfer(
          { ...validatedData, stockableItems: false },
          nonStockableItems
        ),
    ].filter(Boolean);

    const transfers = await Promise.all(transferPromises);
    return transfers.length === 1 ? transfers[0] : transfers;
  } catch (error) {
    throw new Error(`Failed to create transfer request: ${error.message}`);
  }
};

const createSingleTransfer = async (validatedData, items) => {
  const transferNumber = await getNextTransferId(validatedData.tenantId);
  return createTransfer({
    ...validatedData,
    items,
    transferNumber,
    dispatchStatus: transferStatus.PENDING,
    receiveStatus: transferStatus.PENDING,
    transferDate: FD.toFirestore(validatedData.transferDate),
  });
};

/**
 * Create dispatch
 */

const createDispatch = async (
  payload,
  transferIns,
  userId,
  userName,
  directIssue = false
) => {
  const id = transferIns.id;
  try {
    const dispatchNo = await getNextDispatchId(transferIns.tenantId);
    const newDispatchIns = {
      dispatchNo,
      status: transferStatus.PENDING,
      dispatchedBy: {
        id: userId,
        name: userName,
        time: FD.now(),
      },
      items: payload.items
        .filter((item) => item.dispatchedQuantity > 0)
        .map((item) => ({
          itemId: item.itemId,
          pkg: item.pkg,
          dispatchedQuantity: item.dispatchedQuantity,
          receivedQuantity: 0,
          shortageQuantity: 0,
          totalValue: 0,
          wac: 0,
          ledgerId: null,
        })),
    };

    const dispatchRes = await dispatchTransfer(transferIns, newDispatchIns);

    let autoReceive = directIssue;
    if (!autoReceive) {
      if (transferIns.issuer.locationId === transferIns.requester.locationId) {
        const tenantData = await getTenantById(transferIns.tenantId);
        autoReceive = tenantData.settings.autoReceive;
      }
    }

    if (autoReceive) {
      await receiveTransfer(
        {},
        transferIns,
        dispatchNo,
        userId,
        userName,
        true
      );
    }

    return dispatchRes;
  } catch (error) {
    console.log(error, "error");
    throw Error(error.message);
  }
};

/**
 * Get transfers for location
 */
const getTransfers = async (locationId) => {
  return await getTransfersByLocation(locationId);
};

/**
 * Get transfer by ID
 */
const getTransfer = async (id, type) => {
  const transfer = await getTransferById(id, type);
  if (!transfer) throw error("Transfer not found");
  return transfer;
};

/**
 * Dispatch transfer
 */
// @todo: validate why id?
const dispatchTransfer = async (transferIns, dispatchIns) => {
  const info = {
    tenantId: transferIns.tenantId,
    locationId: transferIns.issuer.locationId,
    locationName: transferIns.issuer.locationName,
    inventoryLocationId: transferIns.issuer.id,
    inventoryLocationName: transferIns.issuer.name,
  };

  const ledgers = [];

  for (const item of transferIns.items) {
    const dispatchedItem = dispatchIns.items.find(
      (t) => t.itemId === item.itemId && t.pkg.id === item.pkg.id
    );
    if (!dispatchedItem) {
      continue;
    }

    const dispatchedQty = dispatchedItem.dispatchedQuantity;
    const uom =
      item.pkg && item.pkg.id !== "default" ? item.pkg.name : item.purchaseUOM;

    const ledger = await debitStock({
      ledgerType: LedgerTypes.TRANSFER_OUT,
      ...info,
      itemId: item.itemId,
      itemCode: item.itemCode,
      itemName: item.itemName,
      categoryId: item.categoryId,
      categoryName: item.categoryName,
      subcategoryName: item.subcategoryName,
      subcategoryId: item.subcategoryId,
      pkg: item.pkg,
      qty: dispatchedQty,
      pkgUOM: uom,
      expiryDate: item.expiryDate || new Date(), // @validate date
      grnMeta: null,
      remarks: "",
      eventDate: transferIns.transferDate, // @todo: wrong
    });

    ledgers.push(ledger);

    item.dispatchedQuantity = (item.dispatchedQuantity || 0) + dispatchedQty;
    dispatchedItem.totalValue = ledger.totalCost;
    dispatchedItem.wac = ledger.totalCost / dispatchedQty;
    dispatchedItem.ledgerId = ledger.id;

    item.totalValueDispatch =
      (item.totalValueDispatch || 0) + dispatchedItem.totalValue;
    // item.wacDispatch = item.totalValueDispatch / item.dispatchedQuantity;
  }

  transferIns.timeLine = [dispatchIns, ...(transferIns.timeLine || [])];

  const allDispatched = transferIns.items.every(
    (item) => item.requestedQuantity - item.dispatchedQuantity <= 0
  );
  transferIns.dispatchStatus = allDispatched
    ? transferStatus.COMPLETED
    : transferStatus.PARTIAL;

  await updateTransfer(transferIns.id, transferIns);

  applyLedgersToDailyStock({
    ...info,
    eventDate: transferIns.transferDate,
    ledgers,
  });

  return transferIns;
};

/**
 * Receive transfer
 */
const receiveTransfer = async (
  payload,
  transferIns,
  dispatchId,
  userId,
  userName,
  autoReceive = false
) => {
  const ledgerInfo = {
    tenantId: transferIns.tenantId,
    locationId: transferIns.requester.locationId,
    locationName: transferIns.requester.locationName,
    inventoryLocationId: transferIns.requester.id,
    inventoryLocationName: transferIns.requester.name,
  };

  const dispatchIns = transferIns.timeLine.find(
    (s) => s.dispatchNo === dispatchId
  );

  if (!dispatchIns) {
    throw Error("Dispatch not found");
  }

  if (dispatchIns.status === transferStatus.COMPLETED) {
    throw Error("This dispatch has already been received completely");
  }

  dispatchIns.status = transferStatus.COMPLETED;
  dispatchIns.receivedBy = {
    id: userId,
    name: userName,
    time: FD.now(),
  };
  transferIns.receiveStatus =
    transferIns.dispatchStatus === transferStatus.COMPLETED &&
    transferIns.timeLine.every(
      (entry) => entry.status === transferStatus.COMPLETED
    )
      ? transferStatus.COMPLETED
      : transferStatus.PARTIAL;

  const ledgers = [];

  const transaction = await db.runTransaction(async (trans) => {
    for (const item of transferIns.items) {
      const dispatchedItem = dispatchIns.items.find(
        (t) => t.itemId === item.itemId && t.pkg.id === item.pkg.id
      );

      if (!dispatchedItem) {
        continue;
      }

      let receivedQty = autoReceive ? dispatchedItem.dispatchedQuantity : 0;

      if (!autoReceive) {
        const receivedItem = payload.items.find(
          (i) => i.itemId === item.itemId && i.pkg.id === item.pkg.id
        );

        if (!receivedItem) {
          console.log("Received item not found for", item.itemId);
        }
        receivedQty = receivedItem?.receivedQuantity || 0;
        const shortage = dispatchedItem.dispatchedQuantity - receivedQty;
        dispatchedItem.shortageQuantity = shortage;
        dispatchedItem.reason = receivedItem?.reason || "";
      }

      dispatchedItem.receivedQuantity = receivedQty;
      item.receivedQuantity = (item.receivedQuantity || 0) + receivedQty;
      item.shortageQuantity =
        item.dispatchedQuantity - (item.receivedQuantity || 0);

      const uom =
        item.pkg && item.pkg.id !== "default"
          ? item.pkg.name
          : item.purchaseUOM;

      const wac = dispatchedItem.totalValue / dispatchedItem.dispatchedQuantity;
      const totalValue = wac * receivedQty;

      item.totalValueReceive = (item.totalValueReceive || 0) + totalValue;
      // item.wacReceive = item.totalValueReceive / item.receivedQuantity;

      const ledger = await creditStock(
        {
          ledgerType: LedgerTypes.TRANSFER_IN,
          ...ledgerInfo,
          itemId: item.itemId,
          itemCode: item.itemCode,
          itemName: item.itemName,
          qty: receivedQty,
          pkgUOM: uom,
          taxRate: item.taxRate || 0,
          unitCost: wac,
          totalCost: totalValue,
          expiryDate: item.expiryDate, // @validate date
          grnMeta: null,
          categoryId: item.categoryId,
          subcategoryId: item.subcategoryId,
          categoryName: item.categoryName,
          subcategoryName: item.subcategoryName,
          pkg: item.pkg,
          remarks: item.remarks || null,
          eventDate: transferIns.transferDate,
        },
        trans
      );

      ledgers.push(ledger);
    }

    await handleShortages(transferIns);

    await updateTransfer(transferIns.id, transferIns, trans);
  });

  await transaction;

  applyLedgersToDailyStock({
    ...ledgerInfo,
    eventDate: transferIns.transferDate,
    ledgers,
  });
};

/**
 * Create shortage record
 */
async function handleShortages(data) {
  const shortageRecords = [];

  for (const timeline of data.timeLine) {
    const shortageItems = timeline.items.filter((i) => i.shortageQuantity > 0);
    if (!shortageItems.length) continue;

    const record = {
      tenantId: data.tenantId,
      dispatchNo: timeline.dispatchNo,
      locationId: data.issuer.id,
      inventoryLocationId: data.issuer.locationId,
      createdBy: timeline.receivedBy,
      items: shortageItems.map((t) => {
        const match = data.items.find((d) => d.itemId === t.itemId);
        return {
          itemName: match?.itemName,
          itemId: t.itemId,
          quantity: t.shortageQuantity,
          reason: t.reason,
          type: "reduction",
        };
      }),
    };

    shortageRecords.push(record);
  }

  if (shortageRecords.length) {
    await createShortageRecord(shortageRecords);
  }
}

/**
 * Close transfer
 */
const closeTransfer = async (data, id) => {
  try {
    await db.runTransaction(async (trans) => {
      const updatedData = {
        ...data,
        dispatchStatus: transferStatus.COMPLETED,
        receiveStatus: transferStatus.COMPLETED,
      };
      await updateTransfer(id, updatedData, trans);
    });
  } catch (error) {
    throw Error(error.message);
  }
};

const aggregateTransfers = async (tenantId, filters) => {
  const transfers = await getTransfersRepo(tenantId, filters);
  const result = transfers.map((doc) => ({
    transferNumber: doc.transferNumber,
    id: doc.id,
    grnNumbers: doc.grnNumbers,
    requester: doc.requester.name,
    requesterId: doc.requester.id,
    issuer: doc.issuer.name,
    issuerLocation: doc.issuer.locationName,
    requesterLocation: doc.requester.locationName,
    requestedDate: FD.toFormattedDate(doc.requestedBy.time),
    requestedBy: doc.requestedBy.name,
    dispatchStatus: doc.dispatchStatus,
    receiveStatus: doc.receiveStatus,
    timeLine: doc.timeLine ? doc.timeLine : [],
    stockableItems: doc.stockableItems,
  }));

  result.sort((a, b) => b.transferNumber.localeCompare(a.transferNumber));

  return result;
};

module.exports = {
  createTransferRequest,
  createDispatch,
  getTransfers,
  getTransfer,
  dispatchTransfer,
  receiveTransfer,
  closeTransfer,
  aggregateTransfers,
};
