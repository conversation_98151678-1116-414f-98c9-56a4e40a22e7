const admin = require("firebase-admin");
const db = admin.firestore();
const schema = require("@/models/menuRecipeSchema");
const { FirestoreDateHelper: FD } = require("@/helpers/dateHelper");
const { handleValidation } = require("@/utils/validation");
const {
  saveMenuRecipe,
  getAllMenuRecipes,
  getById,
  updateById,
} = require("@/repositories/menuRecipeRepo");
const { getNextMenuRecipeId } = require("./counterService");
const { LedgerTypes } = require("@/defs/ledgerDefs");
const { creditStock,debitStock } = require("./stockService");
const { getReceipeById } = require("@/repositories/receipeRepo");
const { getInventoryItemStocks } = require("@/repositories/stockRepo");

const createMenuRecipe = async (data) => {
  try {
    const validatedData = handleValidation(data, schema);
    if (!validatedData) return;

    const recipeDataMap = new Map();
    const requiredStockList = [];

    for (const item of validatedData.items) {
      if (!recipeDataMap.has(item.recipeId)) {
        const recipeData = await getReceipeById(data.tenantId, item.recipeId);
        recipeDataMap.set(item.recipeId, recipeData);
      }

      const recipeData = recipeDataMap.get(item.recipeId);
      if (recipeData?.ingredients?.length) {
        for (const ing of recipeData.ingredients) {
          requiredStockList.push({
            itemId: ing.itemId,
            itemName: ing.itemName,
            qty: ing.quantity,
            pkgId: "default"
          });
        }
      }
    }

    await checkStockAvailability(validatedData.workAreaId, requiredStockList);

    const menuRecipeNumber = await getNextMenuRecipeId(data.tenantId);    

    const result = await processTransactions(validatedData, recipeDataMap, menuRecipeNumber);
    return result;

  } catch (error) {
    console.error("Error in createMenuRecipe:", error);
    throw new Error(error.message);
  }
};

const checkStockAvailability = async (workAreaId, requiredStockList) => {
  const stockCheckedItems = await getInventoryItemStocks(
    workAreaId,
    requiredStockList,
    "itemId",
    "pkgId"
  );

  const noStockItems = stockCheckedItems.filter(it => it.recipeQtyInStock === 0);
  if (noStockItems.length > 0) {
    const missing = noStockItems.map(s => s.itemName).join(", ");
    throw new Error(`No stocks found → ${missing}`);
  }

  const shortage = stockCheckedItems.filter(it => it.recipeQtyInStock < it.qty);
  if (shortage.length > 0) {
    // const formatted = shortage
    //   .map(s => `${s.itemName}: Required ${s.qty} ${s.recipeUOM}, In stock ${s.recipeQtyInStock} ${s.recipeUOM}`)
    //   .join(", ");
    const missing = shortage.map(s => s.itemName).join(", ");  
    throw new Error(`Insufficient stocks → ${missing}`);
  }
};

const calculateIngredientQty = (recipeQty,preparedQty,ingredientQty) => {
  const value = preparedQty / recipeQty;
  return ingredientQty * value;
};

const processTransactions = async (validatedData,recipeDataMap,menuRecipeNumber) => {
  return await db.runTransaction(async (trans) => {
    for (const item of validatedData.items) {
      const recipeData = recipeDataMap.get(item.recipeId);

      if (recipeData?.ingredients?.length) {
        const recipeQty = recipeData.quantity;    
        const preparedQty = item.quantity;          

        for (const ing of recipeData.ingredients) {
          const adjustedQty = calculateIngredientQty(recipeQty,preparedQty,ing.quantity);

          // DEBIT STOCK
          // const qty = ing.quantity;
          const pkg = {
            name: ing?.purchaseUnit ? ing.purchaseUnit.symbol : ing.recipeUnit.symbol,
            id: 'default'
          };
          await debitStock(
            {
              ledgerType: LedgerTypes.MENU_RECIPE_DEBIT,
              tenantId: validatedData.tenantId,
              locationId: validatedData.locationId,
              locationName: validatedData.locationName,
              inventoryLocationId: validatedData.workAreaId,
              inventoryLocationName: validatedData.workAreaName,
              itemId: ing.itemId,
              itemCode: ing.itemCode,
              itemName: ing.itemName,
              // qty,
              qtyInRecipeUOM: adjustedQty,
              pkgUOM: pkg.name,
              unitCost: ing.unitCost || 0,
              totalCost: (ing.unitCost || 0) * adjustedQty,
              expiryDate: ing.expiryDate || null,
              grnMeta: null,
              pkg,
              remarks: ing.remarks || null,
              eventDate: validatedData.requestedBy?.time || FD.now(),
            },
            trans
          );
        }
      }

      // CREDIT STOCK
      const qty = item.quantity;
      const pkg = {
        name: item.purchaseUOM,
        id: 'default'
      };
      await creditStock(
        {
          ledgerType: LedgerTypes.MENU_RECIPE_CREDIT,
          tenantId: validatedData.tenantId,
          locationId: validatedData.locationId,
          locationName: validatedData.locationName,
          inventoryLocationId: validatedData.workAreaId,
          inventoryLocationName: validatedData.workAreaName,
          itemId: item.itemId,
          itemCode: item.itemCode,
          itemName: item.itemName,
          qty,
          pkgUOM: item.purchaseUOM,
          unitCost: item.unitCost || 0,
          totalCost: (item.unitCost || 0) * qty,
          expiryDate: item.expiryDate || null,
          grnMeta: null,
          categoryId: item.categoryId,
          subcategoryId: item.subcategoryId,
          categoryName: item.categoryName,
          subcategoryName: item.subcategoryName,
          pkg,
          remarks: item.remarks || null,
        },
        trans
      );
    }

    return await saveMenuRecipe(
      { ...validatedData, menuRecipeNumber },
      trans
    );
  });
};

const getMenuRecipes = async (filters) => {
  const menuRecipes = await getAllMenuRecipes(filters);

  const result = menuRecipes.map((s) => {
    return {
      menuRecipeNumber: s.menuRecipeNumber,
      locationName: s.locationName,
      workAreaName: s.workAreaName,
      id: s.id,
      requestedBy: s.requestedBy.name,
      requestedAt: FD.toFormattedDate(s.requestedBy.time),
    };
  });
  return result;
};

const getMenuRecipeById = async (id) => {
  try {
    const data = await getById(id);
    if (!data) return null;
    return {
      ...data,
      requestedBy: {
        ...data.requestedBy,
        time: FD.toFormattedDate(data.requestedBy.time),
      },
    };
  } catch (error) {
    throw Error(err.message);
  }
};

const updateMenuRecipe = async (id, payload) => {
  try {
    const data = handleValidation(payload, schema);
    if (!data) return;
    const result = await updateById(id, data);
    return result;
  } catch (error) {
    throw Error(err.message);
  }
};

module.exports = {
  createMenuRecipe,
  getMenuRecipes,
  getMenuRecipeById,
  updateMenuRecipe,
};
