const admin = require("firebase-admin");
const db = admin.firestore();

const schema = require("@/models/spoilageSchema");
const { FirestoreDateHelper: FD } = require("@/helpers/dateHelper");
const { handleValidation } = require("@/utils/validation");
const {
  saveSpoilage,
  getAllSpoilages,
  getById,
  updateById,
} = require("@/repositories/spoilageRepo");
const { getNextSpoilageId } = require("./counterService");
const { LedgerTypes } = require("@/defs/ledgerDefs");
const { debitStock } = require("./stockService");

const createSpoilage = async (data) => {
  try {
    const validatedData = handleValidation(data, schema);
    if (!validatedData) return;

    const spoilageNumber = await getNextSpoilageId(data.tenantId);

    const result = await db.runTransaction(async (trans) => {

      if (validatedData.stockCorrection) {

        for (const item of validatedData.items) {
          const uom =
          item.pkg && item.pkg.id !== "default" ? item.pkg.name : item.purchaseUOM; // purchaseUOM required
          const qty = item.spoilageQuantity;
          await debitStock(
            {
              ledgerType: LedgerTypes.SPOILAGE,
              tenantId: validatedData.tenantId,
              locationId: validatedData.locationId,
              locationName: validatedData.locationName,
              inventoryLocationId: validatedData.workAreaId,
              inventoryLocationName: validatedData.workAreaName,
              itemId: item.itemId,
              itemCode: item.itemCode,
              itemName: item.itemName,
              qty,
              pkgUOM: uom,
              // countingUOM: item.countingUOM,
              unitCost: item.unitCost || 0,
              totalCost: (item.unitCost || 0) * qty,
              expiryDate: item.expiryDate || null,
              grnMeta: null,
              categoryId: item.categoryId,
              subcategoryId: item.subcategoryId,
              categoryName: item.categoryName,
              subcategoryName: item.subcategoryName,
              pkg: item.pkg,
              remarks: item.remarks || null,
              eventDate: validatedData.spoilageDate,
            },
            trans
          );
        }
      }

      const requestData = {
        ...validatedData,
        spoilageNumber,
      };

      const spoilageDoc = await saveSpoilage(requestData, trans);
      return spoilageDoc;
    });

    return result;
  } catch (error) {
    console.error("Error in createSpoilage:", error);
    throw new Error(error.message);
  }
};

const getSpoilages = async (filters) => {
  const spoilages = await getAllSpoilages(filters);

  const result = spoilages.map((s) => {
    return {
      spoilageNumber: s.spoilageNumber,
      locationName: s.locationName,
      workAreaName: s.workAreaName,
      id: s.id,
      spoilageDate: FD.toFormattedDate(s.spoilageDate),
      requestedBy: s.requestedBy.name,
      requestedAt: FD.toFormattedDate(s.requestedBy.time),
    };
  });
  return result;
};

const getSpoilageById = async (id) => {
  try {
    const data = await getById(id);
    if (!data) return null;
    return {
      ...data,
      spoilageDate: FD.toFormattedDate(data.spoilageDate),
      requestedBy: {
        ...data.requestedBy,
        time: FD.toFormattedDate(data.requestedBy.time),
      },
    };
  } catch (error) {
    throw Error(err.message);
  }
};

const updateSpoilage = async (id, payload) => {
  try {
    const data = handleValidation(payload, schema);
    if (!data) return;
    const result = await updateById(id, data);
    return result;
  } catch (error) {
    throw Error(err.message);
  }
};

module.exports = {
  createSpoilage,
  getSpoilages,
  getSpoilageById,
  updateSpoilage,
};
